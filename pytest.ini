[tool:pytest]
# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test execution
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes
    --showlocals
    --disable-warnings

# Coverage configuration
addopts_coverage = 
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=90
    --cov-branch

# Async configuration
asyncio_mode = auto

# Markers for test categorization
markers =
    unit: Unit tests that test individual components in isolation
    integration: Integration tests that test multiple components together
    e2e: End-to-end tests that test complete user workflows
    performance: Performance and load testing
    security: Security-focused tests
    slow: Tests that take more than 5 seconds to run
    database: Tests that require database access
    external: Tests that require external services
    auth: Authentication and authorization tests
    api: API endpoint tests
    model: Econometric model tests
    data_processing: Data processing and validation tests
    conflict_analysis: Conflict analysis specific tests
    policy_simulation: Policy simulation tests
    
    # Test categories by complexity
    smoke: Basic smoke tests
    regression: Regression tests
    stress: Stress and endurance tests
    
    # Test categories by environment
    local: Tests that can run locally
    ci: Tests designed for CI environment
    production: Tests that can run against production (read-only)

# Test timeouts (in seconds)
timeout = 300
timeout_method = thread

# Warnings configuration
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pandas.*
    ignore::UserWarning:numpy.*
    ignore::RuntimeWarning:scipy.*
    error::UserWarning:src.*

# Minimum Python version
minversion = 3.11

# Test collection
collect_ignore = 
    setup.py
    conftest.py

# Output options
console_output_style = progress
junit_family = xunit2

# Log configuration for debugging
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = tests.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d %(funcName)s(): %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# Parallel execution (when pytest-xdist is installed)
# addopts_parallel = -n auto

# Custom test execution order
# addopts_order = --order-dependencies