#!/usr/bin/env python3
"""Test core V2 imports without heavy dependencies."""

import sys
import os
# Fix path to point to src directory correctly
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.join(project_root, 'src'))

def test_import(module_path, class_name=None):
    """Test importing a module and optionally a class."""
    try:
        if class_name:
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_path}.{class_name} import successful")
        else:
            __import__(module_path)
            print(f"✅ {module_path} import successful")
        return True
    except Exception as e:
        print(f"❌ {module_path}" + (f".{class_name}" if class_name else "") + f" import failed: {e}")
        return False

def main():
    """Test core V2 system imports."""
    print("=" * 60)
    print("V2 CORE IMPORTS VALIDATION")
    print("=" * 60)
    
    tests = [
        # Domain entities
        ("core.domain.market.entities", "Market"),
        ("core.domain.market.entities", "PriceObservation"),
        ("core.domain.market.entities", "PanelData"),
        
        # Repository interfaces
        ("core.domain.market.repositories", "MarketRepository"),
        ("core.domain.market.repositories", "PriceRepository"),
        
        # Model interfaces
        ("core.models.interfaces.model", "Model"),
        ("core.models.interfaces.estimator", "Estimator"),
        
        # Panel models
        ("core.models.panel.pooled_panel", "PooledPanelModel"),
        ("core.models.panel.fixed_effects", "FixedEffectsModel"),
        
        # Commands
        ("application.commands.run_three_tier_analysis", "RunThreeTierAnalysisCommand"),
        ("application.commands.run_three_tier_analysis", "RunThreeTierAnalysisHandler"),
    ]
    
    passed = 0
    total = len(tests)
    
    for module_path, class_name in tests:
        if test_import(module_path, class_name):
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} imports successful")
    
    if passed == total:
        print("✅ ALL CORE IMPORTS WORKING!")
        print("The V2 system structure is properly set up.")
    else:
        print("❌ SOME IMPORTS FAILED!")
        print("Check missing dependencies or implementation issues.")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)